"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetD<PERSON><PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, Users, UserPlus, FileText, ArrowLeft, DollarSign } from "lucide-react";

interface FormGroupSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicantName: string;
  vehicleName: string;
}

export default function FormGroupSuccessSheet({
  isOpen,
  onClose,
  onBack,
  applicantName,
  vehicleName,
}: FormGroupSuccessSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Co-ownership Group Created
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  First member added successfully
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-6">
              {/* Success Icon */}
              <div className="text-center">
                <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-bold text-[#333333] mb-2">
                  Group Successfully Created
                </h3>
                <p className="text-sm text-[#797879]">
                  {applicantName} is now the first co-owner of your {vehicleName}
                </p>
              </div>

              {/* Group Details */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3 flex items-center">
                  <DollarSign size={16} className="mr-2 text-[#009639]" />
                  Co-ownership Details
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Vehicle:</span>
                    <span className="text-sm font-medium text-[#333333]">{vehicleName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">First Co-owner:</span>
                    <span className="text-sm font-medium text-[#333333]">{applicantName.replace(/\*/g, '')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Group Status:</span>
                    <span className="text-sm font-medium text-[#009639]">Active</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Available Shares:</span>
                    <span className="text-sm font-medium text-[#333333]">Open for more members</span>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                <h4 className="font-semibold text-[#007A2F] mb-3 flex items-center">
                  <UserPlus size={16} className="mr-2" />
                  What Happens Next
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      1
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Finalize Ownership Terms
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Complete the co-ownership agreement with {applicantName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      2
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Accept More Co-owners
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Review additional applications and invite more members to the group
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      3
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Manage Group Activities
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Coordinate usage schedules and shared responsibilities
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Benefits */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Group Benefits
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Shared ownership costs and responsibilities</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Coordinated usage scheduling</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Platform-managed legal agreements</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={onClose}
                  className="ride-primary-btn w-full text-sm py-2"
                >
                  <FileText size={16} className="mr-2" />
                  Complete Ownership Agreement
                </Button>
                <Button
                  onClick={onClose}
                  variant="outline"
                  className="w-full text-sm py-2"
                >
                  <Users size={16} className="mr-2" />
                  Manage Group
                </Button>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

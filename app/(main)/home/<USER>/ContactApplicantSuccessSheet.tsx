"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, MessageCircle, Calendar, FileText, ArrowLeft } from "lucide-react";

interface ContactApplicantSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicantName: string;
  vehicleName: string;
}

export default function ContactApplicantSuccessSheet({
  isOpen,
  onClose,
  onBack,
  applicantName,
  vehicleName,
}: ContactApplicantSuccessSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Contact Initiated
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Rental discussion started
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-6">
              {/* Success Icon */}
              <div className="text-center">
                <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-bold text-[#333333] mb-2">
                  Contact Details Shared
                </h3>
                <p className="text-sm text-[#797879]">
                  You can now discuss rental terms with {applicantName} for your {vehicleName}
                </p>
              </div>

              {/* Next Steps */}
              <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                <h4 className="font-semibold text-[#007A2F] mb-3 flex items-center">
                  <MessageCircle size={16} className="mr-2" />
                  What Happens Next
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      1
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Discuss Rental Terms
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Contact {applicantName} to discuss rental rates, duration, and conditions
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      2
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Schedule Vehicle Handover
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Arrange a convenient time and location for vehicle inspection and handover
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      3
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Finalize Agreement
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Complete the rental agreement through our platform
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Applicant Contact Details
                </h4>
                <div className="space-y-2">
                  <p className="text-sm text-[#797879]">
                    <span className="font-medium">Name:</span> {applicantName.replace(/\*/g, '')}
                  </p>
                  <p className="text-sm text-[#797879]">
                    <span className="font-medium">Email:</span> Full email address revealed
                  </p>
                  <p className="text-sm text-[#797879]">
                    <span className="font-medium">Phone:</span> Contact number provided
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={onClose}
                  className="ride-primary-btn w-full text-sm py-2"
                >
                  <Calendar size={16} className="mr-2" />
                  Schedule Meeting
                </Button>
                <Button
                  onClick={onClose}
                  variant="outline"
                  className="w-full text-sm py-2"
                >
                  <FileText size={16} className="mr-2" />
                  View Rental Agreement Template
                </Button>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";
import { CheckCircle, MessageCircle, Calendar, FileText } from "lucide-react";

interface ContactApplicantSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicantName: string;
  vehicleName: string;
}

export default function ContactApplicantSuccessSheet({
  isOpen,
  onClose,
  onBack,
  applicantName,
  vehicleName,
}: ContactApplicantSuccessSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <CheckCircle size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Contact Initiated!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              You can now discuss rental terms with {applicantName} for your {vehicleName}
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Contact Information */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Applicant Contact Details
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <span className="text-[#009639] font-bold text-xs">
                          @
                        </span>
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Name</p>
                        <p className="text-[#333333] font-medium">
                          {applicantName.replace(/\*/g, '')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <MessageCircle size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Email</p>
                        <p className="text-[#333333] font-medium">
                          Full email address revealed
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <span className="text-[#009639] font-bold text-xs">
                          #
                        </span>
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Phone</p>
                        <p className="text-[#333333] font-medium">
                          Contact number provided
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* What Happens Next */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  What Happens Next?
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        1
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Discuss Rental Terms
                        </p>
                        <p className="text-xs text-[#797879]">
                          Contact {applicantName} to discuss rental rates, duration, and conditions
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        2
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Schedule Vehicle Handover
                        </p>
                        <p className="text-xs text-[#797879]">
                          Arrange a convenient time and location for vehicle inspection and handover
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        3
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Finalize Agreement
                        </p>
                        <p className="text-xs text-[#797879]">
                          Complete the rental agreement through our platform
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Email Confirmation */}
              <div className="rounded-xl bg-green-50 border border-green-200 p-4">
                <div className="flex items-center mb-2">
                  <MessageCircle size={16} className="mr-2 text-green-600" />
                  <h4 className="font-semibold text-green-900">
                    Contact Details Shared
                  </h4>
                </div>
                <p className="text-sm text-green-700">
                  Both parties have been notified and can now communicate directly to arrange the rental.
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={onClose}
              className="w-full bg-[#009639] text-white hover:bg-[#007A2F] py-3 rounded-full font-semibold transition-colors"
            >
              <Calendar size={16} className="mr-2 inline" />
              Schedule Meeting
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              <FileText size={16} className="mr-2 inline" />
              View Rental Agreement Template
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

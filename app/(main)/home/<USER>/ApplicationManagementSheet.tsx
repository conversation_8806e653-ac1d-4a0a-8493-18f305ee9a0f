"use client";

import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import ApplicationManagement from "../../profile/_components/ApplicationManagement";

interface VehicleApplication {
  id: string;
  vehicleId: string;
  vehicleName: string;
  vehicleImage?: string;
  applicantName: string; // Partially redacted: "John D***"
  applicantEmail: string; // Partially redacted: "j***@e***.com"
  applicantAge: number;
  applicantGender: "male" | "female" | "other";
  drivingExperienceYears: number;
  applicationDate: string;
  status: "pending" | "accepted" | "declined";
  listingType: "rental" | "fractional" | "lease-to-own";
  documentsVerified: boolean;
  matchesPreferences: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
  verifiedDocuments: string[];
}

interface ApplicationManagementSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onReviewApplication: (applicationId: string) => void;
}

export default function ApplicationManagementSheet({
  isOpen,
  onClose,
  onBack,
  onReviewApplication,
}: ApplicationManagementSheetProps) {
  // Mock data - replace with actual data fetching
  const [applications, setApplications] = useState<VehicleApplication[]>([
    {
      id: "1",
      vehicleId: "v1",
      vehicleName: "Toyota Corolla 2020",
      applicantName: "John D***",
      applicantEmail: "j***@e***.com",
      applicantAge: 28,
      applicantGender: "male",
      drivingExperienceYears: 5,
      applicationDate: "2024-01-15",
      status: "pending",
      listingType: "rental",
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: true,
        experience: true,
      },
      verifiedDocuments: [
        "ID Document",
        "Driver's License",
        "PrDP Certificate",
        "Insurance Certificate",
      ],
    },
    {
      id: "2",
      vehicleId: "v2",
      vehicleName: "Honda Civic 2019",
      applicantName: "Sarah S***",
      applicantEmail: "s***@g***.com",
      applicantAge: 32,
      applicantGender: "female",
      drivingExperienceYears: 3,
      applicationDate: "2024-01-14",
      status: "accepted",
      listingType: "fractional",
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: false,
        experience: true,
      },
      verifiedDocuments: [
        "ID Document",
        "Driver's License",
        "Proof of Income",
        "Bank Statement",
      ],
    },
    {
      id: "3",
      vehicleId: "v3",
      vehicleName: "BMW X3 2021",
      applicantName: "Mike J***",
      applicantEmail: "m***@h***.com",
      applicantAge: 35,
      applicantGender: "male",
      drivingExperienceYears: 8,
      applicationDate: "2024-01-12",
      status: "pending",
      listingType: "fractional",
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: true,
        experience: true,
      },
      verifiedDocuments: [
        "ID Document",
        "Driver's License",
        "Proof of Income",
        "Bank Statement",
      ],
    },
    {
      id: "4",
      vehicleId: "v3",
      vehicleName: "BMW X3 2021",
      applicantName: "Lisa M***",
      applicantEmail: "l***@y***.com",
      applicantAge: 29,
      applicantGender: "female",
      drivingExperienceYears: 6,
      applicationDate: "2024-01-10",
      status: "declined",
      listingType: "fractional",
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: true,
        experience: false,
      },
      verifiedDocuments: ["ID Document", "Driver's License", "Proof of Income"],
    },
  ]);

  const handleAcceptApplication = (applicationId: string) => {
    console.log("Accepting application:", applicationId);
    // TODO: Implement API call to accept application
    // Update the application status to "accepted"
    setApplications((prev) =>
      prev.map((app) =>
        app.id === applicationId ? { ...app, status: "accepted" as const } : app
      )
    );
  };

  const handleDeclineApplication = (applicationId: string) => {
    console.log("Declining application:", applicationId);
    // TODO: Implement API call to decline application
    // Update the application status to "declined"
    setApplications((prev) =>
      prev.map((app) =>
        app.id === applicationId ? { ...app, status: "declined" as const } : app
      )
    );
  };

  const handleViewFullDetails = (applicationId: string) => {
    console.log("Viewing full details for application:", applicationId);
    // TODO: Implement view full details logic - reveal unredacted information
  };

  const handleFormGroup = (applicationId: string) => {
    console.log("Forming co-ownership group for application:", applicationId);
    // TODO: Implement form group logic - create new co-ownership group
  };

  const handleInviteToGroup = (applicationId: string) => {
    console.log("Inviting to existing group for application:", applicationId);
    // TODO: Implement invite to group logic - add to existing co-ownership group
  };

  const handleContactApplicant = (applicationId: string) => {
    console.log("Contacting applicant for application:", applicationId);
    // TODO: Implement contact logic - start rental discussion flow
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Manage Applications
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Review platform-approved applications for your vehicles
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <ApplicationManagement
              applications={applications}
              onAcceptApplication={handleAcceptApplication}
              onDeclineApplication={handleDeclineApplication}
              onViewFullDetails={handleViewFullDetails}
              onFormGroup={handleFormGroup}
              onInviteToGroup={handleInviteToGroup}
              onContactApplicant={handleContactApplicant}
            />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

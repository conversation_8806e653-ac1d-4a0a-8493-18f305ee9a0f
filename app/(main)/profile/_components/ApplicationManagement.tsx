"use client";

import React from "react";
import {
  CheckCircle,
  XCircle,
  Eye,
  Car,
  Shield,
  FileText,
  MessageCircle,
  Users,
  UserPlus,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface VehicleApplication {
  id: string;
  vehicleId: string;
  vehicleName: string;
  vehicleImage?: string;
  applicantName: string; // Partially redacted: "John D***"
  applicantEmail: string; // Partially redacted: "j***@e***.com"
  applicantAge: number;
  applicantGender: "male" | "female" | "other";
  drivingExperienceYears: number;
  applicationDate: string;
  status: "pending" | "accepted" | "declined";
  listingType: "rental" | "fractional" | "lease-to-own";
  documentsVerified: boolean;
  matchesPreferences: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
  verifiedDocuments: string[];
}

interface ApplicationManagementProps {
  applications: VehicleApplication[];
  onAcceptApplication: (applicationId: string) => void;
  onDeclineApplication: (applicationId: string) => void;
  onViewFullDetails: (applicationId: string) => void;
  onFormGroup: (applicationId: string) => void;
  onInviteToGroup: (applicationId: string) => void;
  onContactApplicant: (applicationId: string) => void;
}

export default function ApplicationManagement({
  applications,
  onAcceptApplication,
  onDeclineApplication,
  onViewFullDetails,
  onFormGroup,
  onInviteToGroup,
  onContactApplicant,
}: ApplicationManagementProps) {
  // Helper function to check if a co-ownership group already exists for a vehicle
  const hasExistingGroup = (vehicleId: string) => {
    // Check if there are any accepted fractional applications for this vehicle
    return applications.some(
      (app) =>
        app.vehicleId === vehicleId &&
        app.listingType === "fractional" &&
        app.status === "accepted"
    );
  };

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "fractional":
        return "Co-ownership";
      case "lease-to-own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  const ApplicationCard = ({
    application,
  }: {
    application: VehicleApplication;
  }) => {
    const groupExists = hasExistingGroup(application.vehicleId);

    return (
      <Card className="mb-4 overflow-hidden drop-shadow-md rounded-xl border border-gray-100">
        <CardContent className="p-0">
          {/* Vehicle Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center">
                <Car size={20} className="text-[#009639]" />
              </div>
              <div>
                <h3 className="font-bold text-[#333333]">
                  {application.vehicleName}
                </h3>
                <p className="text-sm text-[#797879]">
                  {getListingTypeLabel(application.listingType)} Application
                </p>
              </div>
            </div>
          </div>

          {/* Applicant Details */}
          <div className="p-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-[#797879] mb-1">Applicant</p>
                <p className="text-sm font-medium text-[#333333]">
                  {application.applicantName}
                </p>
                <p className="text-xs text-[#797879]">
                  {application.applicantEmail}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879] mb-1">Applied</p>
                <p className="text-sm text-[#333333]">
                  {new Date(application.applicationDate).toLocaleDateString(
                    "en-GB",
                    {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    }
                  )}
                </p>
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-gray-200 my-4"></div>

            {/* Applicant Profile */}
            <div className="bg-[#e6ffe6] rounded-lg p-3 mb-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-xs text-[#797879] mb-1">Age</p>
                  <div className="flex items-center justify-center">
                    <span className="text-sm font-medium text-[#333333]">
                      {application.applicantAge}
                    </span>
                    {application.matchesPreferences.age && (
                      <CheckCircle size={12} className="text-green-500 ml-1" />
                    )}
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-xs text-[#797879] mb-1">Gender</p>
                  <div className="flex items-center justify-center">
                    <span className="text-sm font-medium text-[#333333] capitalize">
                      {application.applicantGender}
                    </span>
                    {application.matchesPreferences.gender && (
                      <CheckCircle size={12} className="text-green-500 ml-1" />
                    )}
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-xs text-[#797879] mb-1">Experience</p>
                  <div className="flex items-center justify-center">
                    <span className="text-sm font-medium text-[#333333]">
                      {application.drivingExperienceYears}y
                    </span>
                    {application.matchesPreferences.experience && (
                      <CheckCircle size={12} className="text-green-500 ml-1" />
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-gray-200 my-4"></div>

            {/* Platform Verified Documents */}
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <Shield size={14} className="text-green-500 mr-2" />
                <span className="text-sm font-medium text-[#333333]">
                  Platform Verified Documents
                </span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {application.verifiedDocuments.map((doc, index) => (
                  <div
                    key={index}
                    className="flex items-center text-xs text-[#797879]"
                  >
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>{doc}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="border-t border-gray-200 pt-4">
              {application.status === "pending" ? (
                // Show Accept/Decline buttons for pending applications
                <div className="flex space-x-2">
                  <Button
                    onClick={() => onAcceptApplication(application.id)}
                    className="ride-primary-btn w-full text-sm py-2 flex-1"
                  >
                    <CheckCircle size={16} className="mr-2" />
                    Accept
                  </Button>
                  <Button
                    onClick={() => onDeclineApplication(application.id)}
                    variant="outline"
                    className="w-full text-sm py-2 flex-1 border-red-200 text-red-600 hover:bg-red-50 rounded-full"
                  >
                    <XCircle size={16} className="mr-2" />
                    Decline
                  </Button>
                </div>
              ) : application.status === "accepted" ? (
                // Show next action buttons only for accepted applications
                application.listingType === "rental" ? (
                  <Button
                    onClick={() => onContactApplicant(application.id)}
                    className="ride-primary-btn w-full text-sm py-2"
                  >
                    <MessageCircle size={16} className="mr-2" />
                    Contact Applicant
                  </Button>
                ) : application.listingType === "fractional" ? (
                  groupExists ? (
                    <Button
                      onClick={() => onInviteToGroup(application.id)}
                      className="ride-primary-btn w-full text-sm py-2"
                    >
                      <Users size={16} className="mr-2" />
                      Invite to Group
                    </Button>
                  ) : (
                    <Button
                      onClick={() => onFormGroup(application.id)}
                      className="ride-primary-btn w-full text-sm py-2"
                    >
                      <UserPlus size={16} className="mr-2" />
                      Form Group
                    </Button>
                  )
                ) : (
                  <Button
                    onClick={() => onViewFullDetails(application.id)}
                    className="ride-primary-btn w-full text-sm py-2"
                  >
                    <Eye size={16} className="mr-2" />
                    View Full Details
                  </Button>
                )
              ) : (
                // For declined applications, show view details
                <Button
                  onClick={() => onViewFullDetails(application.id)}
                  variant="outline"
                  className="w-full text-sm py-2"
                >
                  <Eye size={16} className="mr-2" />
                  View Details
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {/* Info Banner */}
      <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-3">
        <div className="flex items-start">
          <Shield
            size={16}
            className="text-[#009639] mr-2 mt-0.5 flex-shrink-0"
          />
          <div>
            <p className="text-sm font-medium text-[#007A2F] mb-1">
              Platform-Approved Applications
            </p>
            <p className="text-xs text-[#007A2F]">
              These applications have been pre-screened and verified by our
              platform. First accept or decline each application, then take
              action with accepted applicants.
            </p>
          </div>
        </div>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {applications.length === 0 ? (
          <Card className="drop-shadow-md rounded-xl border border-gray-100">
            <CardContent className="p-8 text-center">
              <FileText size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No Applications Yet
              </h3>
              <p className="text-sm text-gray-500">
                Platform-approved applications for your vehicles will appear
                here
              </p>
            </CardContent>
          </Card>
        ) : (
          applications.map((application) => (
            <ApplicationCard key={application.id} application={application} />
          ))
        )}
      </div>
    </div>
  );
}

"use client";

import React from "react";
import {
  User,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Car,
  Shield,
  FileText,
  Mail,
  Phone,
  MapPin,
  Star,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface VehicleApplication {
  id: string;
  vehicleId: string;
  vehicleName: string;
  vehicleImage?: string;
  applicantName: string; // Partially redacted: "John D***"
  applicantEmail: string; // Partially redacted: "j***@e***.com"
  applicantAge: number;
  applicantGender: "male" | "female" | "other";
  drivingExperienceYears: number;
  applicationDate: string;
  status: "pending" | "accepted" | "declined";
  listingType: "rental" | "fractional" | "lease-to-own";
  documentsVerified: boolean;
  matchesPreferences: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
  verifiedDocuments: string[];
}

interface ApplicationManagementProps {
  applications: VehicleApplication[];
  onAcceptApplication: (applicationId: string) => void;
  onDeclineApplication: (applicationId: string) => void;
  onViewDetails: (applicationId: string) => void;
}

export default function ApplicationManagement({
  applications,
  onAcceptApplication,
  onDeclineApplication,
  onViewDetails,
}: ApplicationManagementProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "accepted":
        return "bg-green-100 text-green-800 border-green-200";
      case "declined":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock size={14} />;
      case "accepted":
        return <CheckCircle size={14} />;
      case "declined":
        return <XCircle size={14} />;
      default:
        return <Clock size={14} />;
    }
  };

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "fractional":
        return "Co-ownership";
      case "lease-to-own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  const ApplicationCard = ({
    application,
  }: {
    application: VehicleApplication;
  }) => (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Car size={20} className="text-gray-600" />
            </div>
            <div>
              <h4 className="font-semibold text-[#333333]">
                {application.vehicleName}
              </h4>
              <p className="text-sm text-[#797879]">
                {getListingTypeLabel(application.listingType)} Application
              </p>
            </div>
          </div>
          <Badge
            variant="outline"
            className={`flex items-center gap-1 ${getStatusColor(application.status)}`}
          >
            {getStatusIcon(application.status)}
            <span className="capitalize">{application.status}</span>
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-xs text-[#797879] mb-1">Applicant</p>
            <p className="text-sm font-medium text-[#333333]">
              {application.applicantName}
            </p>
            <p className="text-xs text-[#797879]">
              {application.applicantEmail}
            </p>
          </div>
          <div>
            <p className="text-xs text-[#797879] mb-1">Applied</p>
            <p className="text-sm text-[#333333]">
              {new Date(application.applicationDate).toLocaleDateString(
                "en-GB",
                {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                }
              )}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <p className="text-xs text-[#797879] mb-1">Age</p>
            <div className="flex items-center justify-center">
              <span className="text-sm font-medium text-[#333333]">
                {application.applicantAge}
              </span>
              {application.matchesPreferences.age && (
                <CheckCircle size={12} className="text-green-500 ml-1" />
              )}
            </div>
          </div>
          <div className="text-center">
            <p className="text-xs text-[#797879] mb-1">Gender</p>
            <div className="flex items-center justify-center">
              <span className="text-sm font-medium text-[#333333] capitalize">
                {application.applicantGender}
              </span>
              {application.matchesPreferences.gender && (
                <CheckCircle size={12} className="text-green-500 ml-1" />
              )}
            </div>
          </div>
          <div className="text-center">
            <p className="text-xs text-[#797879] mb-1">Experience</p>
            <div className="flex items-center justify-center">
              <span className="text-sm font-medium text-[#333333]">
                {application.drivingExperienceYears}y
              </span>
              {application.matchesPreferences.experience && (
                <CheckCircle size={12} className="text-green-500 ml-1" />
              )}
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <Shield size={14} className="text-green-500 mr-2" />
            <span className="text-sm font-medium text-[#333333]">
              Platform Verified Documents
            </span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {application.verifiedDocuments.map((doc, index) => (
              <div
                key={index}
                className="flex items-center text-xs text-[#797879]"
              >
                <CheckCircle size={12} className="text-green-500 mr-2" />
                <span>{doc}</span>
              </div>
            ))}
          </div>
        </div>

        {application.status === "pending" && (
          <div className="flex space-x-2">
            <Button
              onClick={() => onAcceptApplication(application.id)}
              className="flex-1 bg-[#009639] hover:bg-[#007A2F] text-white"
              size="sm"
            >
              <CheckCircle size={16} className="mr-2" />
              Accept
            </Button>
            <Button
              onClick={() => onDeclineApplication(application.id)}
              variant="outline"
              className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
              size="sm"
            >
              <XCircle size={16} className="mr-2" />
              Decline
            </Button>
          </div>
        )}

        {application.status !== "pending" && (
          <Button
            onClick={() => onViewDetails(application.id)}
            variant="outline"
            className="w-full"
            size="sm"
          >
            <Eye size={16} className="mr-2" />
            View Details
          </Button>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-4">
      {/* Info Banner */}
      <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-3">
        <div className="flex items-start">
          <Shield
            size={16}
            className="text-[#009639] mr-2 mt-0.5 flex-shrink-0"
          />
          <div>
            <p className="text-sm font-medium text-[#007A2F] mb-1">
              Platform-Approved Applications
            </p>
            <p className="text-xs text-[#007A2F]">
              These applications have been pre-screened and verified by our
              platform. You can review and choose which applicants to accept for
              your vehicles.
            </p>
          </div>
        </div>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {applications.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FileText size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No Applications Yet
              </h3>
              <p className="text-sm text-gray-500">
                Platform-approved applications for your vehicles will appear
                here
              </p>
            </CardContent>
          </Card>
        ) : (
          applications.map((application) => (
            <ApplicationCard key={application.id} application={application} />
          ))
        )}
      </div>
    </div>
  );
}
